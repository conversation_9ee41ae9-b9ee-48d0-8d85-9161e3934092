<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Adhkaar Gallery">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#000000">
    <meta name="description" content="Adhkaar Image Gallery - Offline Mobile Gallery">
    
    <title>Adhkaar Gallery</title>
    
    <link rel="manifest" href="manifest.json">
    <link rel="apple-touch-icon" href="assets/icon-192.png">
    <link rel="icon" type="image/png" href="assets/icon-192.png">
    
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- Main Gallery Container -->
    <div class="gallery-container">
        <!-- Header with Info Button -->
        <div class="header">
            <button class="info-btn" id="infoBtn" aria-label="Book Information">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="10"></circle>
                    <path d="M12 16v-4"></path>
                    <path d="M12 8h.01"></path>
                </svg>
            </button>
        </div>

        <!-- Image Display Area -->
        <div class="image-container" id="imageContainer">
            <img id="currentImage" src="" alt="Gallery Image" class="gallery-image">
            
            <!-- Loading Spinner -->
            <div class="loading-spinner" id="loadingSpinner">
                <div class="spinner"></div>
            </div>
        </div>

        <!-- Navigation Buttons -->
        <button class="nav-btn nav-btn-left" id="prevBtn" aria-label="Previous Image">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="15,18 9,12 15,6"></polyline>
            </svg>
        </button>
        
        <button class="nav-btn nav-btn-right" id="nextBtn" aria-label="Next Image">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="9,18 15,12 9,6"></polyline>
            </svg>
        </button>

        <!-- Page Indicator -->
        <div class="page-indicator" id="pageIndicator">
            <span id="currentPage">1</span> of <span id="totalPages">12</span>
        </div>
    </div>

    <!-- Info Modal -->
    <div class="modal-overlay" id="modalOverlay">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Adhkaar Gallery</h2>
                <button class="close-btn" id="closeBtn" aria-label="Close">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <p>This is a collection of beautiful images from the Adhkaar book. Navigate through the gallery using the arrow buttons or by swiping left and right on your device.</p>
                <p>The gallery works completely offline and can be added to your home screen for a native app-like experience.</p>
                <div class="modal-features">
                    <h3>Features:</h3>
                    <ul>
                        <li>Swipe gestures for navigation</li>
                        <li>Fullscreen mobile experience</li>
                        <li>Offline functionality</li>
                        <li>Smooth transitions</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
