<!DOCTYPE html>
<html>
<head>
    <title>Icon Generator</title>
</head>
<body>
    <h2>Icon Generator for Adhkaar Gallery</h2>
    <p>Click the buttons below to generate and download the required icons:</p>
    
    <button onclick="generateIcon(192)">Generate 192x192 Icon</button>
    <button onclick="generateIcon(512)">Generate 512x512 Icon</button>
    
    <br><br>
    <canvas id="canvas" style="border: 1px solid #ccc;"></canvas>

    <script>
        function generateIcon(size) {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            // Set canvas size
            canvas.width = size;
            canvas.height = size;
            
            // Create gradient background
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#1a1a1a');
            gradient.addColorStop(1, '#4a4a4a');
            
            // Fill background
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // Add border radius effect
            ctx.globalCompositeOperation = 'destination-in';
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, size * 0.15);
            ctx.fill();
            ctx.globalCompositeOperation = 'source-over';
            
            // Draw book/gallery icon
            const centerX = size / 2;
            const centerY = size / 2;
            const iconSize = size * 0.4;
            
            // Draw book pages
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(centerX - iconSize/2, centerY - iconSize/2, iconSize * 0.8, iconSize);
            
            ctx.fillStyle = '#e0e0e0';
            ctx.fillRect(centerX - iconSize/2 + iconSize * 0.1, centerY - iconSize/2, iconSize * 0.8, iconSize);
            
            ctx.fillStyle = '#f0f0f0';
            ctx.fillRect(centerX - iconSize/2 + iconSize * 0.2, centerY - iconSize/2, iconSize * 0.8, iconSize);
            
            // Draw image placeholder
            ctx.fillStyle = '#4CAF50';
            ctx.fillRect(centerX - iconSize/3, centerY - iconSize/3, iconSize * 0.6, iconSize * 0.4);
            
            // Add text
            ctx.fillStyle = '#ffffff';
            ctx.font = `bold ${size * 0.08}px Arial`;
            ctx.textAlign = 'center';
            ctx.fillText('A', centerX, centerY + iconSize * 0.6);
            
            // Download the icon
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `icon-${size}.png`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            });
        }
        
        // Polyfill for roundRect if not available
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.beginPath();
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }
    </script>
</body>
</html>
