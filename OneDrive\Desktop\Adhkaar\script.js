class MobileGallery {
    constructor() {
        this.currentIndex = 0;
        this.totalImages = 12;
        this.images = [];
        this.preloadedImages = new Map();
        this.isLoading = false;
        this.touchStartX = 0;
        this.touchEndX = 0;
        this.minSwipeDistance = 50;
        
        this.initializeImages();
        this.initializeElements();
        this.setupEventListeners();
        this.loadImage(0);
        this.preloadAdjacentImages();
    }

    initializeImages() {
        // Generate image paths
        for (let i = 1; i <= this.totalImages; i++) {
            this.images.push(`assets/images/${i}.jpeg`);
        }
    }

    initializeElements() {
        this.currentImage = document.getElementById('currentImage');
        this.loadingSpinner = document.getElementById('loadingSpinner');
        this.currentPageSpan = document.getElementById('currentPage');
        this.totalPagesSpan = document.getElementById('totalPages');
        this.prevBtn = document.getElementById('prevBtn');
        this.nextBtn = document.getElementById('nextBtn');
        this.infoBtn = document.getElementById('infoBtn');
        this.modalOverlay = document.getElementById('modalOverlay');
        this.closeBtn = document.getElementById('closeBtn');
        this.imageContainer = document.getElementById('imageContainer');
        
        // Set total pages
        this.totalPagesSpan.textContent = this.totalImages;
    }

    setupEventListeners() {
        // Navigation buttons
        this.prevBtn.addEventListener('click', () => this.previousImage());
        this.nextBtn.addEventListener('click', () => this.nextImage());
        
        // Modal controls
        this.infoBtn.addEventListener('click', () => this.openModal());
        this.closeBtn.addEventListener('click', () => this.closeModal());
        this.modalOverlay.addEventListener('click', (e) => {
            if (e.target === this.modalOverlay) {
                this.closeModal();
            }
        });

        // Touch events for swipe gestures
        this.imageContainer.addEventListener('touchstart', (e) => this.handleTouchStart(e), { passive: true });
        this.imageContainer.addEventListener('touchend', (e) => this.handleTouchEnd(e), { passive: true });

        // Keyboard navigation
        document.addEventListener('keydown', (e) => this.handleKeydown(e));

        // Prevent context menu
        document.addEventListener('contextmenu', (e) => e.preventDefault());
        
        // Prevent image dragging
        this.currentImage.addEventListener('dragstart', (e) => e.preventDefault());

        // Handle image load events
        this.currentImage.addEventListener('load', () => this.onImageLoad());
        this.currentImage.addEventListener('error', () => this.onImageError());
    }

    handleTouchStart(e) {
        this.touchStartX = e.changedTouches[0].screenX;
    }

    handleTouchEnd(e) {
        this.touchEndX = e.changedTouches[0].screenX;
        this.handleSwipe();
    }

    handleSwipe() {
        const swipeDistance = this.touchStartX - this.touchEndX;
        
        if (Math.abs(swipeDistance) > this.minSwipeDistance) {
            if (swipeDistance > 0) {
                // Swipe left - next image
                this.nextImage();
            } else {
                // Swipe right - previous image
                this.previousImage();
            }
        }
    }

    handleKeydown(e) {
        switch(e.key) {
            case 'ArrowLeft':
                e.preventDefault();
                this.previousImage();
                break;
            case 'ArrowRight':
                e.preventDefault();
                this.nextImage();
                break;
            case 'Escape':
                this.closeModal();
                break;
        }
    }

    previousImage() {
        if (this.isLoading) return;
        
        this.currentIndex = this.currentIndex === 0 ? this.totalImages - 1 : this.currentIndex - 1;
        this.loadImage(this.currentIndex);
        this.updatePageIndicator();
        this.preloadAdjacentImages();
    }

    nextImage() {
        if (this.isLoading) return;
        
        this.currentIndex = (this.currentIndex + 1) % this.totalImages;
        this.loadImage(this.currentIndex);
        this.updatePageIndicator();
        this.preloadAdjacentImages();
    }

    loadImage(index) {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoading();
        
        const imagePath = this.images[index];
        
        // Check if image is already preloaded
        if (this.preloadedImages.has(imagePath)) {
            this.displayImage(this.preloadedImages.get(imagePath));
            return;
        }
        
        // Load new image
        const img = new Image();
        img.onload = () => {
            this.preloadedImages.set(imagePath, img.src);
            this.displayImage(img.src);
        };
        img.onerror = () => {
            console.error(`Failed to load image: ${imagePath}`);
            this.onImageError();
        };
        img.src = imagePath;
    }

    displayImage(src) {
        // Fade out current image
        this.currentImage.classList.add('fade-out');
        
        setTimeout(() => {
            this.currentImage.src = src;
            this.currentImage.classList.remove('fade-out');
            this.currentImage.classList.add('loaded');
            this.hideLoading();
            this.isLoading = false;
        }, 200);
    }

    preloadAdjacentImages() {
        const prevIndex = this.currentIndex === 0 ? this.totalImages - 1 : this.currentIndex - 1;
        const nextIndex = (this.currentIndex + 1) % this.totalImages;
        
        [prevIndex, nextIndex].forEach(index => {
            const imagePath = this.images[index];
            if (!this.preloadedImages.has(imagePath)) {
                const img = new Image();
                img.onload = () => {
                    this.preloadedImages.set(imagePath, img.src);
                };
                img.src = imagePath;
            }
        });
    }

    showLoading() {
        this.loadingSpinner.style.display = 'block';
    }

    hideLoading() {
        this.loadingSpinner.style.display = 'none';
    }

    onImageLoad() {
        this.currentImage.classList.add('loaded');
        this.hideLoading();
        this.isLoading = false;
    }

    onImageError() {
        this.hideLoading();
        this.isLoading = false;
        console.error('Failed to load image');
    }

    updatePageIndicator() {
        this.currentPageSpan.textContent = this.currentIndex + 1;
    }

    openModal() {
        this.modalOverlay.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    closeModal() {
        this.modalOverlay.classList.remove('active');
        document.body.style.overflow = '';
    }
}

// Initialize gallery when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new MobileGallery();
});

// Prevent zoom on double tap
let lastTouchEnd = 0;
document.addEventListener('touchend', function (event) {
    const now = (new Date()).getTime();
    if (now - lastTouchEnd <= 300) {
        event.preventDefault();
    }
    lastTouchEnd = now;
}, false);

// Hide address bar on mobile
window.addEventListener('load', () => {
    setTimeout(() => {
        window.scrollTo(0, 1);
    }, 100);
});

// Prevent pinch zoom
document.addEventListener('gesturestart', function (e) {
    e.preventDefault();
});

document.addEventListener('gesturechange', function (e) {
    e.preventDefault();
});

document.addEventListener('gestureend', function (e) {
    e.preventDefault();
});
