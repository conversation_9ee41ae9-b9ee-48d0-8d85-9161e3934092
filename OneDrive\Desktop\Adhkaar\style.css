/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    overflow: hidden;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: #000;
    color: #fff;
    user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
}

/* Disable context menu */
body {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Main Gallery Container */
.gallery-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a1a 100%);
}

/* Header */
.header {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 100;
    padding: env(safe-area-inset-top, 20px) 20px 20px 20px;
}

.info-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.info-btn:hover, .info-btn:active {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

/* Image Container */
.image-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    padding: 60px 20px 80px 20px;
}

.gallery-image {
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
    object-fit: contain;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0;
    transform: scale(0.9);
}

.gallery-image.loaded {
    opacity: 1;
    transform: scale(1);
}

.gallery-image.fade-out {
    opacity: 0;
    transform: scale(0.95);
}

/* Loading Spinner */
.loading-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.1);
    border-top: 3px solid #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Navigation Buttons */
.nav-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    z-index: 50;
}

.nav-btn:hover, .nav-btn:active {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-50%) scale(1.1);
}

.nav-btn-left {
    left: 20px;
}

.nav-btn-right {
    right: 20px;
}

/* Page Indicator */
.page-indicator {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.7);
    padding: 12px 24px;
    border-radius: 20px 20px 0 0;
    font-size: 14px;
    font-weight: 500;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-bottom: none;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    padding: 20px;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
    border-radius: 16px;
    max-width: 400px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transform: scale(0.9) translateY(20px);
    transition: all 0.3s ease;
}

.modal-overlay.active .modal-content {
    transform: scale(1) translateY(0);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h2 {
    font-size: 20px;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    color: #fff;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover, .close-btn:active {
    background: rgba(255, 255, 255, 0.1);
}

.modal-body {
    padding: 20px;
}

.modal-body p {
    margin-bottom: 16px;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
}

.modal-features h3 {
    margin-bottom: 12px;
    font-size: 16px;
    color: #fff;
}

.modal-features ul {
    list-style: none;
    padding-left: 0;
}

.modal-features li {
    padding: 6px 0;
    color: rgba(255, 255, 255, 0.8);
    position: relative;
    padding-left: 20px;
}

.modal-features li::before {
    content: '•';
    color: #4CAF50;
    position: absolute;
    left: 0;
    font-weight: bold;
}

/* Touch feedback */
.nav-btn:active,
.info-btn:active,
.close-btn:active {
    transform: scale(0.95);
}

/* Safe area adjustments for notched devices */
@supports (padding: max(0px)) {
    .header {
        padding-top: max(env(safe-area-inset-top), 20px);
    }
    
    .page-indicator {
        padding-bottom: max(env(safe-area-inset-bottom), 12px);
    }
}

/* Landscape orientation adjustments */
@media screen and (orientation: landscape) {
    .image-container {
        padding: 40px 80px 60px 80px;
    }
    
    .nav-btn-left {
        left: 30px;
    }
    
    .nav-btn-right {
        right: 30px;
    }
}
